<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8" />
    <title inertia>{{ config('app.name') }}</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="title" content="{{ config('app.name') }}" />
    <meta name="description" content="Displacement does not silence creativity. It amplifies resilience." />
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#f2f4f7" />
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#1f2937" />
    <link rel="preload" href="/smovee.png" as="image">

    <meta property="og:title" content="{{ config('app.name') }}" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="{{ config('app.url') }}" />
    <meta property="og:image" content="{{ asset('/logo.png') }}" />

    <meta name="twitter:site" content="@smovee" />
    <meta name="twitter:title" content="{{ config('app.name') }}" />
    <meta name="twitter:description" content="Displacement does not silence creativity. It amplifies resilience." />
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:image" content="{{ asset('/logo.png') }}" />

    <link rel="icon" href="/build/favicon.ico" sizes="48x48">
    <link rel="icon" type="image/svg+xml" sizes="any" href="/logo.png" />
    <link rel="apple-touch-icon" href="/build/apple-touch-icon-180x180.png">
    <link rel="manifest" href="/build/manifest.webmanifest">
    {{-- Splash screens --}}
    <link rel="apple-touch-startup-image" media="screen and (device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="/build/apple-splash-portrait-light-1536x2048.png">
    <link rel="apple-touch-startup-image" media="screen and (device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="/build/apple-splash-landscape-light-2048x1536.png">
    <link rel="apple-touch-startup-image" media="screen and (prefers-color-scheme: dark) and (device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="/build/apple-splash-portrait-dark-1536x2048.png">
    <link rel="apple-touch-startup-image" media="screen and (prefers-color-scheme: dark) and (device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="/build/apple-splash-landscape-dark-2048x1536.png">

    <script id="vite-plugin-pwa:register-sw" src="/build/registerSW.js" defer></script>

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <x-theme-loader />

    @viteReactRefresh
    @vite([
    'resources/scripts/styles/index.css',
    'resources/scripts/main.tsx'
    ])
    @inertiaHead
</head>

<body>
    @routes
    @inertia
</body>

</html>
