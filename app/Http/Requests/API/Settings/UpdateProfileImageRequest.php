<?php

namespace App\Http\Requests\API\Settings;

use App\Models\File\Image;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\File;

/**
 * @bodyParam image file required The new profile image (JPG, PNG, max 10MB)
 */
class UpdateProfileImageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'image' => [
                'required',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'image.required' => 'Profile image is required',
            'image.image' => 'File must be a valid image',
            'image.min' => 'Image file is too small',
            'image.max' => 'Image file size cannot exceed ' . (Image::MAX_SIZE / 1024) . 'KB',
        ];
    }
}
