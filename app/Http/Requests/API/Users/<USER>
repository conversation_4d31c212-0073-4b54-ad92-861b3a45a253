<?php

namespace App\Http\Requests\API\Users;

use App\Enums\UserRole;
use App\Models\File\Image;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;

/**
 * @bodyParam name string required The name of the artist. Example: <PERSON>
 * @bodyParam email string required The email address of the artist. Example: <EMAIL>
 * @bodyParam role string required The role of the artist (singer, producer, etc.). Example: singer
 * @bodyParam password string optional The password for the artist account. Example: password123
 * @bodyParam image file optional The profile image for the artist (JPG, PNG, max 10MB)
 */
class CreateArtistRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique(User::class),
            ],
            'role' => [
                'required',
                Rule::enum(UserRole::class),
                Rule::in(User::getOnlyArtistTypes()),
            ],
            'password' => ['nullable', 'string', 'min:8'],
            'image' => [
                'nullable',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Artist name is required',
            'name.max' => 'Artist name cannot exceed 255 characters',
            'email.required' => 'Email address is required',
            'email.email' => 'Please provide a valid email address',
            'email.unique' => 'This email address is already registered',
            'role.required' => 'Artist role is required',
            'role.in' => 'Selected role is not a valid artist type',
            'password.min' => 'Password must be at least 8 characters',
            'image.max' => 'Image file size cannot exceed ' . (Image::MAX_SIZE / 1024) . 'KB',
        ];
    }

    /**
     * Get the available artist roles for documentation
     */
    public function getArtistRoles(): array
    {
        return User::getOnlyArtistTypes();
    }
}
