<?php

namespace App\Http\Requests\API\Articles;

use App\Models\File\Audio;
use App\Models\File\Image;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\File;

/**
 * @bodyParam name string required The name of the article. Example: My New Song
 * @bodyParam description string optional The description of the article. Example: This is my latest track
 * @bodyParam year integer optional The year the article was created. Example: 2024
 * @bodyParam channel_id integer required The ID of the channel this article belongs to. Example: 1
 * @bodyParam genre_id integer required The ID of the genre for this article. Example: 1
 * @bodyParam user_id integer required The ID of the user creating this article. Example: 1
 * @bodyParam featuring array optional Array of featured artist IDs. Example: [2, 3]
 * @bodyParam image file optional The cover image for the article (JPG, PNG, max 10MB)
 * @bodyParam audio file required The audio file for the article (MP3, WAV, etc., max 100MB)
 */
class CreateArticleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // Prepare valid MIME types for audio validation
        $mimeTypes = $this->prepareAudioMimeTypes();

        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:5000'],
            'year' => ['nullable', 'numeric', 'digits:4', 'min:1900', 'max:' . (date('Y') + 1)],
            'channel_id' => ['required', 'integer', 'exists:App\Models\Channel,id'],
            'genre_id' => ['required', 'integer', 'exists:App\Models\Genre,id'],
            'user_id' => ['required', 'integer', 'exists:App\Models\User,id'],
            'featuring' => ['nullable', 'array'],
            'featuring.*' => ['integer', 'exists:App\Models\User,id'],
            'image' => [
                'nullable',
                File::image()
                    ->min(Image::MIN_SIZE)
                    ->max(Image::MAX_SIZE),
            ],
            'audio' => [
                'required',
                File::types($mimeTypes)
                    ->min(Audio::MIN_SIZE)
                    ->max(Audio::MAX_SIZE),
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Article name is required',
            'name.max' => 'Article name cannot exceed 255 characters',
            'year.digits' => 'Year must be a 4-digit number',
            'year.min' => 'Year cannot be before 1900',
            'year.max' => 'Year cannot be in the future',
            'channel_id.required' => 'Channel is required',
            'channel_id.exists' => 'Selected channel does not exist',
            'genre_id.required' => 'Genre is required',
            'genre_id.exists' => 'Selected genre does not exist',
            'user_id.required' => 'User is required',
            'user_id.exists' => 'Selected user does not exist',
            'featuring.array' => 'Featured artists must be an array',
            'featuring.*.exists' => 'One or more featured artists do not exist',
            'audio.required' => 'Audio file is required',
            'audio.max' => 'Audio file size cannot exceed ' . (Audio::MAX_SIZE / 1024) . 'KB',
            'image.max' => 'Image file size cannot exceed ' . (Image::MAX_SIZE / 1024) . 'KB',
        ];
    }

    /**
     * Prepare valid MIME types for audio validation
     */
    private function prepareAudioMimeTypes(): array
    {
        return [
            'mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'wma'
        ];
    }
}
