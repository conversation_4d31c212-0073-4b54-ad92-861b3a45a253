<?php

namespace App\Http\Resources\API\Discovery;

use App\Http\Resources\API\Article\ArticleCollection;
use App\Http\Resources\API\Channel\ChannelCollection;
use App\Http\Resources\API\User\ArtistCollection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Discovery data resource for API responses
 */
class DiscoveryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'recommendations' => [
                'source' => $this->resource['source'] ?? 'unknown',
                'title' => $this->resource['title'] ?? 'Recommendations',
                'type' => $this->resource['type'] ?? 'mixed',
                'items' => $this->transformRecommendationItems($this->resource['items'] ?? []),
            ],
            'recently_played' => new ChannelCollection($this->resource['recently_played'] ?? []),
            'top_charts' => new ArticleCollection($this->resource['top_charts'] ?? []),
            'top_artists' => new ArtistCollection($this->resource['top_artists'] ?? []),
        ];
    }

    /**
     * Transform recommendation items based on their type
     */
    private function transformRecommendationItems(array $items): array
    {
        return collect($items)->map(function ($item) {
            if (isset($item['type'])) {
                switch ($item['type']) {
                    case 'channel':
                        return new ChannelCollection($item['items'] ?? []);
                    case 'article':
                        return new ArticleCollection($item['items'] ?? []);
                    case 'playlist':
                        // Assuming you have a PlaylistCollection
                        return $item['items'] ?? [];
                    default:
                        return $item['items'] ?? [];
                }
            }

            return $item;
        })->toArray();
    }
}
