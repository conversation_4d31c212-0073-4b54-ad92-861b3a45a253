<?php

namespace App\Http\Resources\API\Common;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Generic success response resource for API endpoints
 */
class SuccessResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'success' => true,
            'message' => $this->resource['message'] ?? 'Operation completed successfully',
            'data' => $this->resource['data'] ?? null,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Create a success response with custom message
     */
    public static function make(string $message = 'Operation completed successfully', mixed $data = null): self
    {
        return new self([
            'message' => $message,
            'data' => $data,
        ]);
    }
}
