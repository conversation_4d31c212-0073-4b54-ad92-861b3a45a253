<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\Search\SearchRequest;
use App\Http\Resources\API\Search\SearchResultResource;
use App\Repositories\ArticleRepository;
use App\Repositories\ChannelRepository;
use App\Repositories\GenreRepository;
use App\Repositories\UserRepository;
use Dedoc\Scramble\Attributes\Group;

/**
 * APIs for searching content across the platform
 */
#[Group('Search', weight: 3)]
class SearchController extends Controller
{
    public function __construct(
        private GenreRepository $genreRepository,
        private UserRepository $userRepository,
        private ChannelRepository $channelRepository,
        private ArticleRepository $articleRepository,
    ) {}

    /**
     * Search content
     *
     * Search for artists, channels, articles, and genres across the platform.
     *
     * @authenticated
     *
     * @response 200 {
     *   "data": {
     *     "query": "rock music",
     *     "results": {
     *       "artists": {
     *         "data": [],
     *         "meta": {
     *           "total": 0
     *         }
     *       },
     *       "channels": {
     *         "data": [],
     *         "meta": {
     *           "total": 0
     *         }
     *       },
     *       "articles": {
     *         "data": [],
     *         "meta": {
     *           "total": 0
     *         }
     *       },
     *       "genres": []
     *     },
     *     "meta": {
     *       "total_results": 0,
     *       "search_time": null
     *     }
     *   }
     * }
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "q": ["Search query is required"]
     *   }
     * }
     */
    public function search(SearchRequest $request): SearchResultResource
    {
        $startTime = microtime(true);

        $query = $request->getQuery();
        $limit = $request->getLimit();

        $results = [
            'query' => $query,
            'artists' => $this->userRepository->searchArtists($query, $limit),
            'channels' => $this->channelRepository->searchChannels($query, $limit),
            'articles' => $this->articleRepository->searchArticles($query, $limit),
            'genres' => $this->genreRepository->searchGenres($query, $limit),
            'search_time' => round((microtime(true) - $startTime) * 1000, 2).'ms',
        ];

        return new SearchResultResource($results);
    }
}
