<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\API\Common\SuccessResource;
use App\Models\User;
use Illuminate\Http\Request;

/**
 * @group Following
 *
 * APIs for managing user follows/subscriptions
 */
class FollowController extends Controller
{
    /**
     * Toggle follow status
     *
     * Toggles the follow status for a specific artist. If already following, it will unfollow; if not following, it will follow.
     *
     * @authenticated
     *
     * @urlParam user integer required The ID of the artist to follow/unfollow. Example: 1
     *
     * @response 200 {
     *   "data": {
     *     "success": true,
     *     "message": "Artist followed successfully",
     *     "data": {
     *       "is_following": true,
     *       "followers_count": 101
     *     },
     *     "timestamp": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     * @response 400 {
     *   "message": "Cannot follow yourself."
     * }
     * @response 404 {
     *   "message": "User not found."
     * }
     */
    public function toggle(User $user, Request $request): SuccessResource
    {
        $artist = $user;
        $auth = $request->user();

        // Prevent users from following themselves
        if ($artist->id === $auth->id) {
            abort(400, 'Cannot follow yourself');
        }

        $followed = $artist->followers()->where('follower_id', $auth->id)->exists();

        if ($followed) {
            // Unfollow
            $artist->followers()->where('follower_id', $auth->id)->delete();

            return SuccessResource::success(
                'Artist unfollowed successfully',
                [
                    'is_following' => false,
                    'followers_count' => $artist->fresh()->followers_count ?? 0,
                ]
            );
        } else {
            // Follow
            $artist->followers()->create(['follower_id' => $auth->id]);

            return SuccessResource::success(
                'Artist followed successfully',
                [
                    'is_following' => true,
                    'followers_count' => $artist->fresh()->followers_count ?? 0,
                ]
            );
        }
    }
}
