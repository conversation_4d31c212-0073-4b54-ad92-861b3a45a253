<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\API\Article\ArticleCollection;
use App\Http\Resources\API\Channel\ChannelCollection;
use App\Http\Resources\API\User\ArtistCollection;
use App\Repositories\ArticleRepository;
use App\Repositories\ChannelRepository;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;

/**
 * @group Favorites
 *
 * APIs for managing user favorites and liked content
 */
class FavoriteController extends Controller
{
    public function __construct(
        private ArticleRepository $articleRepository,
        private ChannelRepository $channelRepository,
        private UserRepository $userRepository
    ) {}

    /**
     * Get user favorites
     *
     * Returns all favorited content for the authenticated user including liked articles, channels, and followed artists.
     *
     * @authenticated
     *
     * @queryParam limit integer optional Number of items per category (default: 20). Example: 10
     *
     * @response 200 {
     *   "data": {
     *     "articles": {
     *       "data": [],
     *       "meta": {
     *         "total": 0
     *       }
     *     },
     *     "channels": {
     *       "data": [],
     *       "meta": {
     *         "total": 0
     *       }
     *     },
     *     "following": {
     *       "data": [],
     *       "meta": {
     *         "total": 0
     *       }
     *     }
     *   }
     * }
     */
    public function index(Request $request): array
    {
        $limit = $request->input('limit', 20);

        return [
            'articles' => new ArticleCollection(
                $this->articleRepository->getUserLikesArticles($request->user(), $limit)
            ),
            'channels' => new ChannelCollection(
                $this->channelRepository->getUserLikesChannels($request->user(), $limit)
            ),
            'following' => new ArtistCollection(
                $this->userRepository->getUserFollowings($request->user(), $limit)
            ),
        ];
    }

    /**
     * Get favorite articles
     *
     * Returns only the articles that the user has liked.
     *
     * @authenticated
     *
     * @queryParam limit integer optional Number of articles to return (default: 100). Example: 50
     *
     * @response 200 {
     *   "data": [],
     *   "meta": {
     *     "total": 0
     *   }
     * }
     */
    public function articles(Request $request): ArticleCollection
    {
        $limit = $request->input('limit', 100);

        return new ArticleCollection(
            $this->articleRepository->getUserFavoriteArticles($request->user(), $limit)
        );
    }

    /**
     * Get favorite channels
     *
     * Returns only the channels that the user has liked.
     *
     * @authenticated
     *
     * @queryParam limit integer optional Number of channels to return (default: 100). Example: 50
     *
     * @response 200 {
     *   "data": [],
     *   "meta": {
     *     "total": 0
     *   }
     * }
     */
    public function channels(Request $request): ChannelCollection
    {
        $limit = $request->input('limit', 100);

        return new ChannelCollection(
            $this->channelRepository->getUserLikesChannels($request->user(), $limit)
        );
    }

    /**
     * Get followed artists
     *
     * Returns only the artists that the user is following.
     *
     * @authenticated
     *
     * @queryParam limit integer optional Number of artists to return (default: 100). Example: 50
     *
     * @response 200 {
     *   "data": [],
     *   "meta": {
     *     "total": 0
     *   }
     * }
     */
    public function following(Request $request): ArtistCollection
    {
        $limit = $request->input('limit', 100);

        return new ArtistCollection(
            $this->userRepository->getUserFollowings($request->user(), $limit)
        );
    }
}
