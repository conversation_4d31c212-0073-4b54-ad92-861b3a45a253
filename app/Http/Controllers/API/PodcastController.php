<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\API\Channel\ChannelCollection;
use App\Repositories\ChannelRepository;
use Illuminate\Http\Request;

/**
 * @group Podcasts
 *
 * APIs for managing podcast content
 */
class PodcastController extends Controller
{
    public function __construct(
        private ChannelRepository $channelRepository
    ) {}

    /**
     * Get all podcasts
     *
     * Returns a paginated list of all podcast channels.
     *
     * @authenticated
     *
     * @queryParam limit integer optional Number of podcasts per page (default: 20). Example: 10
     *
     * @response 200 {
     *   "data": [],
     *   "meta": {
     *     "total": 0
     *   }
     * }
     */
    public function index(Request $request): ChannelCollection
    {
        $limit = $request->input('limit', 20);
        $podcasts = $this->channelRepository->getChannelTypePodcasts($limit);

        return new ChannelCollection($podcasts);
    }
}
