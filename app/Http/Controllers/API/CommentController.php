<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\Comments\CreateCommentRequest;
use App\Http\Requests\API\Comments\UpdateCommentRequest;
use App\Http\Resources\API\Comment\CommentApiResource;
use App\Http\Resources\API\Comment\CommentCollection;
use App\Http\Resources\API\Common\SuccessResource;
use App\Models\Article;
use App\Models\Channel;
use App\Models\Morph\Comment;
use Illuminate\Http\Request;

/**
 * @group Comments
 *
 * APIs for managing comments on articles and channels
 */
class CommentController extends Controller
{
    /**
     * Get article comments
     *
     * Returns paginated comments for a specific article.
     *
     * @authenticated
     *
     * @urlParam article integer required The ID of the article. Example: 1
     *
     * @queryParam page integer optional Page number for pagination (default: 1). Example: 2
     *
     * @response 200 {
     *   "data": [],
     *   "meta": {
     *     "total": 0,
     *     "per_page": 30,
     *     "current_page": 1,
     *     "last_page": 1,
     *     "from": null,
     *     "to": null
     *   },
     *   "links": {
     *     "first": "http://example.com/api/articles/1/comments?page=1",
     *     "last": "http://example.com/api/articles/1/comments?page=1",
     *     "prev": null,
     *     "next": null
     *   }
     * }
     * @response 404 {
     *   "message": "Article not found."
     * }
     */
    public function articleComments(Article $article): CommentCollection
    {
        $comments = $article
            ->comments()
            ->with('user')
            ->latest()
            ->paginate(30);

        return new CommentCollection($comments);
    }

    /**
     * Get channel comments
     *
     * Returns paginated comments for a specific channel.
     *
     * @authenticated
     *
     * @urlParam channel integer required The ID of the channel. Example: 1
     *
     * @queryParam page integer optional Page number for pagination (default: 1). Example: 2
     *
     * @response 200 {
     *   "data": [],
     *   "meta": {
     *     "total": 0,
     *     "per_page": 30,
     *     "current_page": 1,
     *     "last_page": 1,
     *     "from": null,
     *     "to": null
     *   },
     *   "links": {
     *     "first": "http://example.com/api/channels/1/comments?page=1",
     *     "last": "http://example.com/api/channels/1/comments?page=1",
     *     "prev": null,
     *     "next": null
     *   }
     * }
     * @response 404 {
     *   "message": "Channel not found."
     * }
     */
    public function channelComments(Channel $channel): CommentCollection
    {
        $comments = $channel
            ->comments()
            ->with('user')
            ->latest()
            ->paginate(30);

        return new CommentCollection($comments);
    }

    /**
     * Create article comment
     *
     * Creates a new comment on a specific article.
     *
     * @authenticated
     *
     * @urlParam article integer required The ID of the article. Example: 1
     *
     * @response 201 {
     *   "data": {
     *     "id": 1,
     *     "comment": "This is a great song!",
     *     "user": {},
     *     "can_edit": true,
     *     "can_delete": true,
     *     "commentable": {
     *       "type": "App\\Models\\Article",
     *       "id": 1
     *     },
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "comment": ["Comment text is required"]
     *   }
     * }
     */
    public function storeArticleComment(Article $article, CreateCommentRequest $request): CommentApiResource
    {
        $data = $request->validated();

        $comment = $article->comments()->create([
            'user_id' => $request->user()->id,
            'comment' => $data['comment'],
        ]);

        $comment->load('user');

        return new CommentApiResource($comment);
    }

    /**
     * Create channel comment
     *
     * Creates a new comment on a specific channel.
     *
     * @authenticated
     *
     * @urlParam channel integer required The ID of the channel. Example: 1
     *
     * @response 201 {
     *   "data": {
     *     "id": 1,
     *     "comment": "This is a great channel!",
     *     "user": {},
     *     "can_edit": true,
     *     "can_delete": true,
     *     "commentable": {
     *       "type": "App\\Models\\Channel",
     *       "id": 1
     *     },
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "comment": ["Comment text is required"]
     *   }
     * }
     */
    public function storeChannelComment(Channel $channel, CreateCommentRequest $request): CommentApiResource
    {
        $data = $request->validated();

        $comment = $channel->comments()->create([
            'user_id' => $request->user()->id,
            'comment' => $data['comment'],
        ]);

        $comment->load('user');

        return new CommentApiResource($comment);
    }

    /**
     * Update comment
     *
     * Updates an existing comment. Only the comment author can update their comment.
     *
     * @authenticated
     *
     * @urlParam comment integer required The ID of the comment. Example: 1
     *
     * @response 200 {
     *   "data": {
     *     "id": 1,
     *     "comment": "This is an updated comment!",
     *     "user": {},
     *     "can_edit": true,
     *     "can_delete": true,
     *     "commentable": {
     *       "type": "App\\Models\\Article",
     *       "id": 1
     *     },
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     * @response 401 {
     *   "message": "Unauthorized."
     * }
     * @response 404 {
     *   "message": "Comment not found."
     * }
     */
    public function update(Comment $comment, UpdateCommentRequest $request): CommentApiResource
    {
        if ($comment->user_id !== $request->user()->id) {
            abort(401, 'Unauthorized');
        }

        $data = $request->validated();
        $comment->fill($data)->save();
        $comment->refresh();
        $comment->load('user');

        return new CommentApiResource($comment);
    }

    /**
     * Delete comment
     *
     * Deletes an existing comment. Only the comment author can delete their comment.
     *
     * @authenticated
     *
     * @urlParam comment integer required The ID of the comment. Example: 1
     *
     * @response 200 {
     *   "data": {
     *     "success": true,
     *     "message": "Comment deleted successfully",
     *     "data": null,
     *     "timestamp": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     * @response 401 {
     *   "message": "Unauthorized."
     * }
     * @response 404 {
     *   "message": "Comment not found."
     * }
     */
    public function destroy(Comment $comment, Request $request): SuccessResource
    {
        if ($comment->user_id !== $request->user()->id) {
            abort(401, 'Unauthorized');
        }

        $comment->delete();

        return SuccessResource::success('Comment deleted successfully');
    }
}
