<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\API\Discovery\DiscoveryResource;
use App\Repositories\RecommendationRepository;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Http\Request;

/**
 * APIs for content discovery and recommendations
 */
#[Group('Discovery', weight: 2)]
class DiscoveryController extends Controller
{
    public function __construct(
        private RecommendationRepository $recommendationRepository
    ) {}

    /**
     * Get discovery content
     *
     * Returns personalized recommendations, recently played content, top charts, and top artists.
     */
    public function index(Request $request): JsonResponse
    {
        $recommendations = $this->recommendationRepository->getUserPlaylistRecommendation();
        $recentlyPlayed = $this->recommendationRepository->recentlyPlayedChannels();

        $recommendationsLessSource = [
            'global_popular_guest',
            'global_popular_fallback',
            'no_recommendations_available',
        ];

        $topArtists = $this->recommendationRepository->getTopArtists(limit: 5);
        $topCharts = [];

        if (! in_array($recommendations['source'], $recommendationsLessSource)) {
            $topCharts = $this->recommendationRepository->getGlobalPopularTracks(limit: 6);
        }

        // Convert Laravel Resource collections to arrays for consistent API response
        $recentlyPlayedData = $recentlyPlayed ? $recentlyPlayed->toArray() : [];
        $topChartsData = $topCharts ? $topCharts->toArray() : [];
        $topArtistsData = $topArtists ? $topArtists->toArray() : [];
        $playlistsData = $recommendations['playlists'] ? $recommendations['playlists']->toArray() : [];

        return response()->json([
            'data' => [
                'recommendations' => [
                    'source' => $recommendations['source'] ?? 'unknown',
                    'title' => $this->getRecommendationTitle($recommendations['source'] ?? 'unknown'),
                    'type' => 'mixed',
                    'items' => $playlistsData,
                ],
                'recently_played' => [
                    'data' => $recentlyPlayedData,
                    'meta' => [
                        'total' => count($recentlyPlayedData),
                    ],
                ],
                'top_charts' => [
                    'data' => $topChartsData,
                    'meta' => [
                        'total' => count($topChartsData),
                    ],
                ],
                'top_artists' => [
                    'data' => $topArtistsData,
                    'meta' => [
                        'total' => count($topArtistsData),
                    ],
                ],
            ],
        ]);
    }

    /**
     * Get recommendation title based on source
     */
    private function getRecommendationTitle(string $source): string
    {
        return match ($source) {
            'personalized_playlists' => 'Your Personalized Playlists',
            'global_popular_fallback' => 'Popular Right Now',
            'no_recommendations_available' => 'Discover Music',
            default => 'Recommendations',
        };
    }
}
