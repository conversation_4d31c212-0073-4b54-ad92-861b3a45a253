<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\API\Discovery\DiscoveryResource;
use App\Repositories\RecommendationRepository;
use Illuminate\Http\Request;

/**
 * @group Discovery
 * 
 * APIs for content discovery and recommendations
 */
class DiscoveryController extends Controller
{
    public function __construct(
        private RecommendationRepository $recommendationRepository
    ) {}

    /**
     * Get discovery content
     * 
     * Returns personalized recommendations, recently played content, top charts, and top artists.
     * 
     * @authenticated
     * 
     * @response 200 {
     *   "data": {
     *     "recommendations": {
     *       "source": "user_preferences",
     *       "title": "Recommended for You",
     *       "type": "mixed",
     *       "items": []
     *     },
     *     "recently_played": {
     *       "data": [],
     *       "meta": {
     *         "total": 0
     *       }
     *     },
     *     "top_charts": {
     *       "data": [],
     *       "meta": {
     *         "total": 0
     *       }
     *     },
     *     "top_artists": {
     *       "data": [],
     *       "meta": {
     *         "total": 0
     *       }
     *     }
     *   }
     * }
     * 
     * @response 401 {
     *   "message": "Unauthenticated."
     * }
     */
    public function index(Request $request): DiscoveryResource
    {
        $recommendations = $this->recommendationRepository->getUserPlaylistRecommendation();
        $recentlyPlayed = $this->recommendationRepository->recentlyPlayedChannels();
        
        $recommendationsLessSource = [
            'global_popular_guest',
            'global_popular_fallback',
            'no_recommendations_available',
        ];

        $topArtists = $this->recommendationRepository->getTopArtists(limit: 5);
        $topCharts = [];
        
        if (! in_array($recommendations['source'], $recommendationsLessSource)) {
            $topCharts = $this->recommendationRepository->getGlobalPopularTracks(limit: 6);
        }

        return new DiscoveryResource([
            ...$recommendations,
            'recently_played' => $recentlyPlayed,
            'top_charts' => $topCharts,
            'top_artists' => $topArtists,
        ]);
    }
}
