<?php

namespace App\Http\Controllers\API;

use App\Filament\HasFileRelation;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\Articles\CreateArticleRequest;
use App\Http\Resources\API\Article\ArticleApiResource;
use App\Models\Article;
use App\Models\File\Audio;
use App\Models\File\Image;
use App\Repositories\FeaturingRepository;

/**
 * @group Articles
 * 
 * APIs for managing music articles/tracks
 */
class ArticleController extends Controller
{
    use HasFileRelation;

    public function __construct(
        private FeaturingRepository $featuringRepository
    ) {}

    /**
     * Get article details
     * 
     * Returns detailed information about a specific article.
     * 
     * @authenticated
     * 
     * @urlParam article integer required The ID of the article. Example: 1
     * 
     * @response 200 {
     *   "data": {
     *     "id": 1,
     *     "name": "My Song",
     *     "description": "A great song",
     *     "year": 2024,
     *     "slug": "my-song",
     *     "duration": "3:45",
     *     "plays_count": 100,
     *     "likes_count": 25,
     *     "comments_count": 5,
     *     "is_liked": false,
     *     "image": {
     *       "id": 1,
     *       "url": "https://example.com/storage/images/cover.jpg",
     *       "filename": "cover.jpg"
     *     },
     *     "audio": {
     *       "id": 1,
     *       "url": "https://example.com/storage/audio/song.mp3",
     *       "filename": "song.mp3",
     *       "size": 5242880,
     *       "mime_type": "audio/mpeg"
     *     },
     *     "artist": {},
     *     "channel": {},
     *     "genre": {},
     *     "featuring": [],
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     * 
     * @response 404 {
     *   "message": "Article not found."
     * }
     */
    public function show(Article $article): ArticleApiResource
    {
        $article->load(['user', 'channel', 'genre', 'image', 'audio', 'featurings.user']);
        
        return new ArticleApiResource($article);
    }

    /**
     * Create a new article
     * 
     * Creates a new music article/track with audio file and optional cover image.
     * 
     * @authenticated
     * 
     * @response 201 {
     *   "data": {
     *     "id": 1,
     *     "name": "My New Song",
     *     "description": "A great new song",
     *     "year": 2024,
     *     "slug": "my-new-song",
     *     "duration": null,
     *     "plays_count": 0,
     *     "likes_count": 0,
     *     "comments_count": 0,
     *     "is_liked": false,
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     * 
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "name": ["The name field is required."],
     *     "audio": ["The audio field is required."]
     *   }
     * }
     */
    public function store(CreateArticleRequest $request): ArticleApiResource
    {
        $data = $request->validated();

        // Create the article
        $article = Article::create($data);

        // Handle file uploads
        $this->handleImageUpload($request, $article);
        $this->handleAudioUpload($request, $article);

        // Handle featured artists
        $this->handleFeaturedArtists($request, $data, $article);

        $article->refresh();
        $article->load(['user', 'channel', 'genre', 'image', 'audio', 'featurings.user']);

        return new ArticleApiResource($article);
    }

    /**
     * Handle image upload
     */
    private function handleImageUpload(CreateArticleRequest $request, Article $article): void
    {
        if (! $request->hasFile('image')) {
            return;
        }

        $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

        $article->image()->create(
            $this->fillFileRequiredFields(
                disk: Image::getDisk(),
                data: ['filename_disk' => $path]
            )
        );
    }

    /**
     * Handle audio upload
     */
    private function handleAudioUpload(CreateArticleRequest $request, Article $article): void
    {
        if (! $request->hasFile('audio')) {
            return;
        }

        $path = $request->file('audio')->store(Audio::FOLDER, Audio::getDisk());

        $article->audio()->create(
            $this->fillFileRequiredFields(
                disk: Audio::getDisk(),
                data: ['filename_disk' => $path]
            )
        );
    }

    /**
     * Handle featured artists
     */
    private function handleFeaturedArtists(CreateArticleRequest $request, array $data, Article $article): void
    {
        if (empty($data['featuring'])) {
            return;
        }

        foreach ($data['featuring'] as $userId) {
            $this->featuringRepository->createArticleFeaturing($article, $userId);
        }
    }
}
