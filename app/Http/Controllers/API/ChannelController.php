<?php

namespace App\Http\Controllers\API;

use App\Filament\HasFileRelation;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\Channels\CreateChannelRequest;
use App\Http\Resources\API\Article\ArticleCollection;
use App\Http\Resources\API\Channel\ChannelApiResource;
use App\Http\Resources\API\Channel\ChannelCollection;
use App\Models\Channel;
use App\Models\File\Image;
use App\Repositories\ChannelRepository;
use Illuminate\Http\Request;

/**
 * @group Channels
 * 
 * APIs for managing music channels/albums
 */
class ChannelController extends Controller
{
    use HasFileRelation;

    public function __construct(
        private ChannelRepository $channelRepository
    ) {}

    /**
     * Get all channels
     * 
     * Returns a paginated list of all channels.
     * 
     * @authenticated
     * 
     * @queryParam limit integer optional Number of channels per page (default: 20). Example: 10
     * 
     * @response 200 {
     *   "data": [],
     *   "meta": {
     *     "total": 0
     *   }
     * }
     */
    public function index(Request $request): ChannelCollection
    {
        $limit = $request->input('limit', 20);
        $channels = $this->channelRepository->getRandomChannels($limit);
        
        return new ChannelCollection($channels);
    }

    /**
     * Get channel details
     * 
     * Returns detailed information about a specific channel.
     * 
     * @authenticated
     * 
     * @urlParam channel integer required The ID of the channel. Example: 1
     * 
     * @response 200 {
     *   "data": {
     *     "id": 1,
     *     "name": "My Channel",
     *     "description": "A great channel",
     *     "slug": "my-channel",
     *     "type": "music",
     *     "articles_count": 10,
     *     "likes_count": 25,
     *     "comments_count": 5,
     *     "is_liked": false,
     *     "image": {
     *       "id": 1,
     *       "url": "https://example.com/storage/images/cover.jpg",
     *       "filename": "cover.jpg"
     *     },
     *     "artist": {},
     *     "genre": {},
     *     "latest_articles": [],
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     * 
     * @response 404 {
     *   "message": "Channel not found."
     * }
     */
    public function show(Channel $channel): ChannelApiResource
    {
        $channel->load(['user', 'genre', 'image', 'latestArticles']);
        
        return new ChannelApiResource($channel);
    }

    /**
     * Get channel articles
     * 
     * Returns all articles/tracks in a specific channel.
     * 
     * @authenticated
     * 
     * @urlParam channel integer required The ID of the channel. Example: 1
     * @queryParam limit integer optional Number of articles per page (default: 20). Example: 10
     * 
     * @response 200 {
     *   "data": [],
     *   "meta": {
     *     "total": 0
     *   }
     * }
     * 
     * @response 404 {
     *   "message": "Channel not found."
     * }
     */
    public function articles(Channel $channel, Request $request): ArticleCollection
    {
        $limit = $request->input('limit', 20);
        $articles = $channel->articles()
            ->with(['user', 'image', 'audio', 'genre'])
            ->latest()
            ->paginate($limit);
        
        return new ArticleCollection($articles);
    }

    /**
     * Create a new channel
     * 
     * Creates a new music channel/album with optional cover image.
     * 
     * @authenticated
     * 
     * @response 201 {
     *   "data": {
     *     "id": 1,
     *     "name": "My New Channel",
     *     "description": "A great new channel",
     *     "slug": "my-new-channel",
     *     "type": "music",
     *     "articles_count": 0,
     *     "likes_count": 0,
     *     "comments_count": 0,
     *     "is_liked": false,
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     * 
     * @response 422 {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "name": ["The name field is required."],
     *     "type": ["The type field is required."]
     *   }
     * }
     */
    public function store(CreateChannelRequest $request): ChannelApiResource
    {
        $data = $request->validated();

        $channel = Channel::create($data);

        if ($request->hasFile('image')) {
            $path = $request->file('image')->storePublicly(Image::FOLDER, Image::getDisk());

            $channel->image()->create(
                $this->fillFileRequiredFields(
                    disk: Image::getDisk(),
                    data: ['filename_disk' => $path]
                )
            );
        }

        $channel->refresh();
        $channel->load(['user', 'genre', 'image']);

        return new ChannelApiResource($channel);
    }
}
