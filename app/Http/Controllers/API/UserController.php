<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\API\User\UserProfileResource;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Http\Request;

/**
 * APIs for managing user profiles and authentication status
 */
#[Group('User Management', weight: 1)]
class UserController extends Controller
{
    /**
     * Get authenticated user profile
     *
     * Returns the profile information of the currently authenticated user.
     *
     * @authenticated
     *
     * @response 200 {
     *   "data": {
     *     "id": 1,
     *     "name": "<PERSON>",
     *     "email": "<EMAIL>",
     *     "role": "user",
     *     "is_artist": false,
     *     "image": {
     *       "id": 1,
     *       "url": "https://example.com/storage/images/profile.jpg",
     *       "filename": "profile.jpg"
     *     },
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-01-01T00:00:00.000000Z",
     *     "stats": {
     *       "followers_count": 10,
     *       "following_count": 5
     *     }
     *   }
     * }
     * @response 401 {
     *   "message": "Unauthenticated."
     * }
     */
    public function profile(Request $request): UserProfileResource
    {
        return new UserProfileResource($request->user());
    }
}
