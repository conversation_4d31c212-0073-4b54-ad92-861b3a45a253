<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\API\Common\SuccessResource;
use Illuminate\Http\Request;

/**
 * @group Notifications
 *
 * APIs for managing user notifications
 */
class NotificationController extends Controller
{
    /**
     * Clear all notifications
     *
     * Clears all notifications for the authenticated user.
     *
     * @authenticated
     *
     * @response 200 {
     *   "data": {
     *     "success": true,
     *     "message": "All notifications cleared successfully",
     *     "data": {
     *       "cleared_count": 5
     *     },
     *     "timestamp": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     */
    public function clear(Request $request): SuccessResource
    {
        $user = $request->user();
        $clearedCount = $user->notifications()->count();

        $user->notifications()->delete();

        return SuccessResource::success(
            'All notifications cleared successfully',
            [
                'cleared_count' => $clearedCount,
            ]
        );
    }

    /**
     * Delete specific notification
     *
     * Deletes a specific notification by its ID.
     *
     * @authenticated
     *
     * @urlParam notificationId string required The ID of the notification to delete. Example: 550e8400-e29b-41d4-a716-446655440000
     *
     * @response 200 {
     *   "data": {
     *     "success": true,
     *     "message": "Notification deleted successfully",
     *     "data": null,
     *     "timestamp": "2024-01-01T00:00:00.000000Z"
     *   }
     * }
     * @response 404 {
     *   "message": "Notification not found."
     * }
     */
    public function destroy(string $notificationId, Request $request): SuccessResource
    {
        /** @var \App\Models\User */
        $user = $request->user();

        $notification = $user->notifications()->findOrFail($notificationId);
        $notification->delete();

        return SuccessResource::success('Notification deleted successfully');
    }
}
