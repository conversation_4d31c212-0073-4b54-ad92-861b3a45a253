{"openapi": "3.1.0", "info": {"title": "Smovee", "version": "0.0.1"}, "servers": [{"url": "http://localhost:8000/api"}], "tags": [{"name": "User Management"}, {"name": "Discovery"}, {"name": "Search"}, {"name": "Artists"}, {"name": "Articles"}], "paths": {"/user": {"get": {"operationId": "user.profile", "description": "Returns the profile information of the currently authenticated user.", "summary": "Get authenticated user profile", "tags": ["User Management"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/discover": {"get": {"operationId": "discovery.index", "description": "Returns personalized recommendations, recently played content, top charts, and top artists.", "summary": "Get discovery content", "tags": ["Discovery"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/search": {"get": {"operationId": "search.search", "description": "Search for artists, channels, articles, and genres across the platform.", "summary": "Search content", "tags": ["Search"], "parameters": [{"name": "q", "in": "query", "required": true, "schema": {"type": "string", "minLength": 1, "maxLength": 255}}, {"name": "limit", "in": "query", "schema": {"type": ["integer", "null"], "minimum": 1, "maximum": 100}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/artists": {"get": {"operationId": "artist.index", "description": "Returns a paginated list of all artists.", "summary": "Get all artists", "tags": ["Artists"], "parameters": [{"name": "limit", "in": "query", "schema": {"type": "string", "default": 20}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "artist.store", "description": "Creates a new artist account with optional profile image.", "summary": "Create a new artist", "tags": ["Artists"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateArtistRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [201]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/artists/{user}": {"get": {"operationId": "artist.show", "description": "Returns detailed information about a specific artist.", "summary": "Get artist details", "tags": ["Artists"], "parameters": [{"name": "user", "in": "path", "required": true, "description": "The user ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/artists/{user}/channels": {"get": {"operationId": "artist.channels", "description": "Returns all channels/albums created by a specific artist.", "summary": "Get artist channels", "tags": ["Artists"], "parameters": [{"name": "user", "in": "path", "required": true, "description": "The user ID", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "schema": {"type": "string", "default": 20}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/artists/search/name": {"get": {"operationId": "artist.searchByName", "description": "Search for artists by their name.", "summary": "Search artists by name", "tags": ["Artists"], "parameters": [{"name": "q", "in": "query", "required": true, "schema": {"type": "string", "minLength": 1}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/artists/search/email": {"get": {"operationId": "artist.searchByEmail", "description": "Search for artists by their email address.", "summary": "Search artists by email", "tags": ["Artists"], "parameters": [{"name": "q", "in": "query", "required": true, "schema": {"type": "string", "format": "email"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/articles/{article}": {"get": {"operationId": "article.show", "description": "Returns detailed information about a specific article.", "summary": "Get article details", "tags": ["Articles"], "parameters": [{"name": "article", "in": "path", "required": true, "description": "The article ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/articles": {"post": {"operationId": "article.store", "description": "Creates a new music article/track with audio file and optional cover image.", "summary": "Create a new article", "tags": ["Articles"], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/CreateArticleRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [201]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/channels": {"get": {"operationId": "channel.index", "description": "Returns a paginated list of all channels.", "summary": "Get all channels", "tags": ["Channel"], "parameters": [{"name": "limit", "in": "query", "schema": {"type": "string", "default": 20}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "channel.store", "description": "Creates a new music channel/album with optional cover image.", "summary": "Create a new channel", "tags": ["Channel"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChannelRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [201]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/channels/{channel}": {"get": {"operationId": "channel.show", "description": "Returns detailed information about a specific channel.", "summary": "Get channel details", "tags": ["Channel"], "parameters": [{"name": "channel", "in": "path", "required": true, "description": "The channel ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/channels/{channel}/articles": {"get": {"operationId": "channel.articles", "description": "Returns all articles/tracks in a specific channel.", "summary": "Get channel articles", "tags": ["Channel"], "parameters": [{"name": "channel", "in": "path", "required": true, "description": "The channel ID", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "schema": {"type": "string", "default": 20}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/comments/{comment}": {"put": {"operationId": "comment.update", "description": "Updates an existing comment. Only the comment author can update their comment.", "summary": "Update comment", "tags": ["Comment"], "parameters": [{"name": "comment", "in": "path", "required": true, "description": "The comment ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCommentRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}, "delete": {"operationId": "comment.destroy", "description": "Deletes an existing comment. Only the comment author can delete their comment.", "summary": "Delete comment", "tags": ["Comment"], "parameters": [{"name": "comment", "in": "path", "required": true, "description": "The comment ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}}}}, "/articles/{article}/comments": {"get": {"operationId": "comment.articleComments", "description": "Returns paginated comments for a specific article.", "summary": "Get article comments", "tags": ["Comment"], "parameters": [{"name": "article", "in": "path", "required": true, "description": "The article ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "comment.storeArticleComment", "description": "Creates a new comment on a specific article.", "summary": "Create article comment", "tags": ["Comment"], "parameters": [{"name": "article", "in": "path", "required": true, "description": "The article ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCommentRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [201]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/channels/{channel}/comments": {"get": {"operationId": "comment.channelComments", "description": "Returns paginated comments for a specific channel.", "summary": "Get channel comments", "tags": ["Comment"], "parameters": [{"name": "channel", "in": "path", "required": true, "description": "The channel ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "comment.storeChannelComment", "description": "Creates a new comment on a specific channel.", "summary": "Create channel comment", "tags": ["Comment"], "parameters": [{"name": "channel", "in": "path", "required": true, "description": "The channel ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCommentRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [201]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/favorites": {"get": {"operationId": "favorite.index", "description": "Returns all favorited content for the authenticated user including liked articles, channels, and followed artists.", "summary": "Get user favorites", "tags": ["Favorite"], "parameters": [{"name": "limit", "in": "query", "schema": {"type": "string", "default": 20}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/favorites/articles": {"get": {"operationId": "favorite.articles", "description": "Returns only the articles that the user has liked.", "summary": "Get favorite articles", "tags": ["Favorite"], "parameters": [{"name": "limit", "in": "query", "schema": {"type": "string", "default": 100}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/favorites/channels": {"get": {"operationId": "favorite.channels", "description": "Returns only the channels that the user has liked.", "summary": "Get favorite channels", "tags": ["Favorite"], "parameters": [{"name": "limit", "in": "query", "schema": {"type": "string", "default": 100}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/favorites/following": {"get": {"operationId": "favorite.following", "description": "Returns only the artists that the user is following.", "summary": "Get followed artists", "tags": ["Favorite"], "parameters": [{"name": "limit", "in": "query", "schema": {"type": "string", "default": 100}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/follow/{user}": {"put": {"operationId": "follow.toggle", "description": "Toggles the follow status for a specific artist. If already following, it will unfollow; if not following, it will follow.", "summary": "Toggle follow status", "tags": ["Follow"], "parameters": [{"name": "user", "in": "path", "required": true, "description": "The user ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "400": {"description": "An error", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview.", "example": "Cannot follow yourself"}}, "required": ["message"]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/auth/token": {"post": {"operationId": "issueAuthToken", "description": "Authenticates a user and returns an API token for subsequent requests.", "summary": "Issue authentication token", "tags": ["IssueAuthToken"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/likes/articles/{article}": {"put": {"operationId": "like.toggleArticleLike", "description": "Toggles the like status for a specific article. If already liked, it will unlike; if not liked, it will like.", "summary": "Toggle article like", "tags": ["Like"], "parameters": [{"name": "article", "in": "path", "required": true, "description": "The article ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/likes/channels/{channel}": {"put": {"operationId": "like.toggleChannelLike", "description": "Toggles the like status for a specific channel. If already liked, it will unlike; if not liked, it will like.", "summary": "Toggle channel like", "tags": ["Like"], "parameters": [{"name": "channel", "in": "path", "required": true, "description": "The channel ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/notifications/clear": {"post": {"operationId": "notification.clear", "description": "Clears all notifications for the authenticated user.", "summary": "Clear all notifications", "tags": ["Notification"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/notifications/{notificationId}": {"delete": {"operationId": "notification.destroy", "description": "Deletes a specific notification by its ID.", "summary": "Delete specific notification", "tags": ["Notification"], "parameters": [{"name": "notificationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/plays/articles/{article}": {"put": {"operationId": "play.recordArticlePlay", "description": "Records a play/listen event for a specific article. This is used for analytics and play count tracking.", "summary": "Record article play", "tags": ["Play"], "parameters": [{"name": "article", "in": "path", "required": true, "description": "The article ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/podcasts": {"get": {"operationId": "podcast.index", "description": "Returns a paginated list of all podcast channels.", "summary": "Get all podcasts", "tags": ["Podcast"], "parameters": [{"name": "limit", "in": "query", "schema": {"type": "string", "default": 20}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/settings/account": {"get": {"operationId": "settings.account", "description": "Returns account settings and permissions for the authenticated user.", "summary": "Get account settings", "tags": ["Settings"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/settings/profile-image": {"post": {"operationId": "settings.updateProfileImage", "description": "Updates the profile image for the authenticated user.", "summary": "Update profile image", "tags": ["Settings"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileImageRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [200]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/settings/artist-request": {"post": {"operationId": "settings.createArtistRequest", "description": "Creates a request for the user to become an artist on the platform.", "summary": "Create artist request", "tags": ["Settings"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateArtistRequestRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "integer", "enum": [201]}}}}, "403": {"$ref": "#/components/responses/AuthorizationException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}}, "components": {"schemas": {"ChannelType": {"type": "string", "enum": ["CHANNEL", "ALBUM", "SINGLE"], "title": "ChannelType"}, "CreateArticleRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 255}, "description": {"type": ["string", "null"], "maxLength": 5000}, "year": {"type": ["number", "null"], "minimum": 1900, "maximum": 2026}, "channel_id": {"type": "integer"}, "genre_id": {"type": "integer"}, "user_id": {"type": "integer"}, "image": {"type": ["string", "null"]}, "audio": {"type": "string", "format": "binary", "contentMediaType": "application/octet-stream"}, "featuring": {"type": ["array", "null"], "items": {"type": "integer"}}}, "required": ["name", "channel_id", "genre_id", "user_id", "audio"], "title": "CreateArticleRequest"}, "CreateArtistRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 255}, "email": {"type": "string", "format": "email", "maxLength": 255}, "role": {"$ref": "#/components/schemas/UserRole"}, "password": {"type": ["string", "null"], "minLength": 8}, "image": {"type": ["string", "null"]}}, "required": ["name", "email", "role"], "title": "CreateArtistRequest"}, "CreateArtistRequestRequest": {"type": "object", "properties": {"artist_type": {"type": "string", "enum": ["SINGER", "PODCASTER"], "maxLength": 10}, "description": {"type": "string", "minLength": 20, "maxLength": 5000}}, "required": ["artist_type", "description"], "title": "CreateArtistRequestRequest"}, "CreateChannelRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 255}, "description": {"type": ["string", "null"], "maxLength": 5000}, "type": {"$ref": "#/components/schemas/ChannelType"}, "genre_id": {"type": "integer"}, "user_id": {"type": "integer"}, "image": {"type": ["string", "null"]}}, "required": ["name", "type", "genre_id", "user_id"], "title": "CreateChannelRequest"}, "CreateCommentRequest": {"type": "object", "properties": {"comment": {"type": "string", "minLength": 2, "maxLength": 1000}}, "required": ["comment"], "title": "CreateCommentRequest"}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string"}, "device_name": {"type": "string"}}, "required": ["email", "password", "device_name"], "title": "LoginRequest"}, "UpdateCommentRequest": {"type": "object", "properties": {"comment": {"type": "string", "minLength": 2, "maxLength": 1000}}, "required": ["comment"], "title": "UpdateCommentRequest"}, "UpdateProfileImageRequest": {"type": "object", "properties": {"image": {"type": "string"}}, "required": ["image"], "title": "UpdateProfileImageRequest"}, "UserRole": {"type": "string", "enum": ["GUEST", "SINGER", "PODCASTER", "ADMIN"], "title": "UserRole"}}, "responses": {"ValidationException": {"description": "Validation error", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Errors overview."}, "errors": {"type": "object", "description": "A detailed description of each field that failed validation.", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}, "required": ["message", "errors"]}}}}, "AuthenticationException": {"description": "Unauthenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview."}}, "required": ["message"]}}}}, "ModelNotFoundException": {"description": "Not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview."}}, "required": ["message"]}}}}, "AuthorizationException": {"description": "Authorization error", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview."}}, "required": ["message"]}}}}}}}